# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        aws.yml                                                           #
# Version:                                                                        #
#               2025-03-21 espy                                                   #
# Create Date:  2025-03-21                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

aws_ocp:
  bastion_ntp_enabled: false
  bastion_libvirt_enabled: false
  platform: aws
  baremetal_network_cidr: "{{ lookup('lmco.openshift.cidr_from_subnet_id', ocp.aws.baremetal_subnet_id) }}"
  bmc_api: aws
  cluster_lb_enabled: false
  https_non_root_port: 443
  dns_port: 53
  bastion_is_gateway: false
  bastion_firewall_enabled: false
  nodes_tpm_encryption: false
  file_transfer_method: ssh
  aws:
    worker_count: 2
    master_count: 3
